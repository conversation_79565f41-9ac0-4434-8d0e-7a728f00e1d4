# 指定 freemarker 的模板路径和模板的后缀
spring:
  freemarker:
    template-loader-path: classpath:/webapp/
    suffix: .ftl
    # 指定字符集
    charset: utf-8
    # 指定是否要启用缓存
    cache: false
    # 指定是否要暴露请求和会话属性
    expose-request-attributes: true
    expose-session-attributes: true
  # spring 静态资源扫描路径
  web:
    resources:
      static-locations: classpath:/static/
  # 数据库配置
  datasource:
    url: ********************************************************************************************************************
    username: datax_web
    password: datax_web
    driver-class-name: com.mysql.cj.jdbc.Driver
    # 连接池配置
    hikari:
      minimum-idle: 5
      maximum-pool-size: 20
      auto-commit: true
      idle-timeout: 30000
      pool-name: LoadForecastHikariCP
      max-lifetime: 1800000
      connection-timeout: 30000

# 下面这些内容是为了让 MyBatis 映射
# 指定 Mybatis 的 Mapper 文件
mybatis:
  mapper-locations: classpath:mappers/*xml
  # 指定 Mybatis 的实体目录
  type-aliases-package: com.thtf.opengis.loadforecast.entity
  # MyBatis配置
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: true
    lazy-loading-enabled: true
    multiple-result-sets-enabled: true
    use-column-label: true
    use-generated-keys: false
    auto-mapping-behavior: partial
    default-executor-type: simple
    default-statement-timeout: 25000

# 应用服务 WEB 访问端口
server:
  port: 8080

# 负荷预测系统配置
loadforecast:
  algorithm:
    default-f1: 1.0
    min-sample-ratio: 0.05
    split-std-threshold: 0.1
    default-k1: 1.0
    default-k2: 1.0
    heating-stages:
      early-cold:
        design-temperature: -5.0
        base-temperature: -3.0
      severe-cold:
        design-temperature: -8.0
        base-temperature: -6.0
      late-cold:
        design-temperature: 0.0
        base-temperature: 2.0
  data-governance:
    min-temperature: -30.0
    max-temperature: 20.0
    min-efficiency: 0.0
    max-efficiency: 200.0
    min-heat: 0.0
    max-heat: 1000000.0
    outlier-threshold: 3.0
  assessment:
    similarity-threshold: 0.05
    accuracy-threshold: 0.9
    difference-alert-threshold: 0.1
    energy-exceeded-threshold: 0.2

# 日志配置
logging:
  level:
    com.thtf.opengis.loadforecast: DEBUG
    org.springframework.web: INFO
    org.mybatis: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
