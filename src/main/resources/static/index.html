<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>供热负荷预测系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .content {
            padding: 40px;
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }

        .feature-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            border-left: 5px solid #3498db;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }

        .feature-card h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.3em;
        }

        .feature-card p {
            color: #666;
            line-height: 1.6;
            margin-bottom: 15px;
        }

        .feature-list {
            list-style: none;
        }

        .feature-list li {
            color: #555;
            margin-bottom: 8px;
            padding-left: 20px;
            position: relative;
        }

        .feature-list li:before {
            content: "✓";
            color: #27ae60;
            font-weight: bold;
            position: absolute;
            left: 0;
        }

        .demo-section {
            background: #ecf0f1;
            border-radius: 10px;
            padding: 30px;
            margin-top: 30px;
        }

        .demo-section h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            text-align: center;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: #2c3e50;
            font-weight: bold;
        }

        .form-group input, .form-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: #3498db;
        }

        .form-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .btn {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            margin: 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
        }

        .btn-success {
            background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
        }

        .btn-success:hover {
            box-shadow: 0 5px 15px rgba(39, 174, 96, 0.4);
        }

        .result-section {
            background: white;
            border-radius: 10px;
            padding: 25px;
            margin-top: 20px;
            border: 2px solid #ecf0f1;
            display: none;
        }

        .result-section.show {
            display: block;
            animation: fadeIn 0.5s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .result-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #ecf0f1;
        }

        .result-item:last-child {
            border-bottom: none;
        }

        .result-label {
            font-weight: bold;
            color: #2c3e50;
        }

        .result-value {
            color: #3498db;
            font-size: 1.1em;
        }

        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }

        .error {
            background: #e74c3c;
            color: white;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }

        .success {
            background: #27ae60;
            color: white;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }

        .api-section {
            background: #2c3e50;
            color: white;
            padding: 30px;
            margin-top: 30px;
            border-radius: 10px;
        }

        .api-section h2 {
            margin-bottom: 20px;
        }

        .api-endpoint {
            background: #34495e;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
        }

        .method {
            color: #e74c3c;
            font-weight: bold;
        }

        .url {
            color: #f39c12;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>供热负荷预测系统</h1>
            <p>基于历史数据与动态修正的智能供热负荷预测平台</p>
        </div>

        <div class="content">
            <div class="feature-grid">
                <div class="feature-card">
                    <h3>🎯 三维评价体系</h3>
                    <p>实现预测指标、标杆指标、实际指标的三维评价体系，全面评估系统运行状态。</p>
                    <ul class="feature-list">
                        <li>预测指标：算法预测的热指标值</li>
                        <li>标杆指标：理论最优的热指标值</li>
                        <li>实际指标：系统实际运行的热指标值</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <h3>🔧 动态修正算法</h3>
                    <p>采用历史修正系数K1和实时修正系数K2的双重修正机制，提高预测精度。</p>
                    <ul class="feature-list">
                        <li>历史修正系数K1：基于历史数据计算</li>
                        <li>实时修正系数K2：基于当年数据动态更新</li>
                        <li>温度区间自适应调整</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <h3>🧠 智能评估系统</h3>
                    <p>自动评估系统运行状态，提供智能化的优化建议和预警机制。</p>
                    <ul class="feature-list">
                        <li>系统正常：维持现有策略</li>
                        <li>能耗超标：触发警报排查</li>
                        <li>系统最优：保持当前工况</li>
                        <li>模型修正：调整预测参数</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <h3>📊 数据治理模块</h3>
                    <p>完善的数据清洗、标准化和质量评估功能，确保数据质量。</p>
                    <ul class="feature-list">
                        <li>异常值检测和处理</li>
                        <li>缺失数据插值补全</li>
                        <li>数据标准化处理</li>
                        <li>数据质量评估报告</li>
                    </ul>
                </div>
            </div>

            <div class="demo-section">
                <h2>🚀 负荷预测演示</h2>
                <form id="forecastForm">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="period">采暖期</label>
                            <input type="text" id="period" value="2023-2024" required>
                        </div>
                        <div class="form-group">
                            <label for="forecastTarget">预测对象</label>
                            <select id="forecastTarget" required>
                                <option value="1">热网</option>
                                <option value="2">热源</option>
                                <option value="3">换热站</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="targetId">对象ID</label>
                            <input type="number" id="targetId" value="1" required>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="targetName">对象名称</label>
                            <input type="text" id="targetName" value="测试热网" required>
                        </div>
                        <div class="form-group">
                            <label for="forecastDate">预测日期</label>
                            <input type="date" id="forecastDate" required>
                        </div>
                        <div class="form-group">
                            <label for="temperature">外温 (℃)</label>
                            <input type="number" id="temperature" step="0.1" value="-5.0" required>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="weatherCondition">天气状况</label>
                            <select id="weatherCondition" required>
                                <option value="晴天">晴天</option>
                                <option value="阴天">阴天</option>
                                <option value="小雪">小雪</option>
                                <option value="大雪">大雪</option>
                            </select>
                        </div>
                    </div>

                    <div style="text-align: center;">
                        <button type="submit" class="btn">执行预测</button>
                        <button type="button" class="btn btn-success" onclick="runBatchDemo()">批量预测演示</button>
                    </div>
                </form>

                <div id="resultSection" class="result-section">
                    <h3>预测结果</h3>
                    <div id="resultContent"></div>
                </div>
            </div>

            <div class="api-section">
                <h2>📡 API 接口文档</h2>
                <div class="api-endpoint">
                    <span class="method">POST</span> <span class="url">/api/loadforecast/forecast</span> - 执行单次负荷预测
                </div>
                <div class="api-endpoint">
                    <span class="method">POST</span> <span class="url">/api/loadforecast/forecast/batch</span> - 批量负荷预测
                </div>
                <div class="api-endpoint">
                    <span class="method">PUT</span> <span class="url">/api/loadforecast/actual/{id}</span> - 更新实际数据
                </div>
                <div class="api-endpoint">
                    <span class="method">GET</span> <span class="url">/api/loadforecast/history</span> - 获取预测历史
                </div>
                <div class="api-endpoint">
                    <span class="method">GET</span> <span class="url">/api/loadforecast/accuracy</span> - 获取预测准确率统计
                </div>
                <div class="api-endpoint">
                    <span class="method">POST</span> <span class="url">/api/loadforecast/assessment</span> - 系统评估
                </div>
            </div>
        </div>
    </div>

    <script>
        // 设置默认日期为今天
        document.getElementById('forecastDate').value = new Date().toISOString().split('T')[0];

        // 表单提交处理
        document.getElementById('forecastForm').addEventListener('submit', function(e) {
            e.preventDefault();
            performForecast();
        });

        async function performForecast() {
            const formData = {
                period: document.getElementById('period').value,
                forecastTarget: parseInt(document.getElementById('forecastTarget').value),
                targetId: parseInt(document.getElementById('targetId').value),
                targetName: document.getElementById('targetName').value,
                forecastDate: document.getElementById('forecastDate').value,
                temperature: parseFloat(document.getElementById('temperature').value),
                weatherCondition: document.getElementById('weatherCondition').value
            };

            showLoading();

            try {
                const response = await fetch('/api/loadforecast/forecast', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(formData)
                });

                const result = await response.json();

                if (result.success) {
                    showResult(result.data);
                } else {
                    showError(result.message || '预测失败');
                }
            } catch (error) {
                showError('网络错误: ' + error.message);
            }
        }

        async function runBatchDemo() {
            const batchData = {
                period: "2023-2024",
                forecastTarget: 1,
                targetId: 1,
                targetName: "演示热网",
                items: [
                    { forecastDate: "2024-01-01", temperature: -5.0, weatherCondition: "晴天" },
                    { forecastDate: "2024-01-02", temperature: -6.0, weatherCondition: "阴天" },
                    { forecastDate: "2024-01-03", temperature: -7.0, weatherCondition: "小雪" },
                    { forecastDate: "2024-01-04", temperature: -8.0, weatherCondition: "大雪" },
                    { forecastDate: "2024-01-05", temperature: -4.0, weatherCondition: "晴天" }
                ]
            };

            showLoading();

            try {
                const response = await fetch('/api/loadforecast/forecast/batch', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(batchData)
                });

                const result = await response.json();

                if (result.success) {
                    showBatchResult(result.data);
                } else {
                    showError(result.message || '批量预测失败');
                }
            } catch (error) {
                showError('网络错误: ' + error.message);
            }
        }

        function showLoading() {
            const resultSection = document.getElementById('resultSection');
            const resultContent = document.getElementById('resultContent');

            resultContent.innerHTML = '<div class="loading">正在计算中...</div>';
            resultSection.classList.add('show');
        }

        function showResult(data) {
            const resultContent = document.getElementById('resultContent');

            resultContent.innerHTML = `
                <div class="result-item">
                    <span class="result-label">预测热量:</span>
                    <span class="result-value">${data.forecastHeat ? data.forecastHeat.toFixed(2) : 'N/A'} MW</span>
                </div>
                <div class="result-item">
                    <span class="result-label">热指标:</span>
                    <span class="result-value">${data.efficiency ? data.efficiency.toFixed(2) : 'N/A'} W/m²</span>
                </div>
                <div class="result-item">
                    <span class="result-label">供热面积:</span>
                    <span class="result-value">${data.area ? data.area.toFixed(0) : 'N/A'} m²</span>
                </div>
                <div class="result-item">
                    <span class="result-label">预测日期:</span>
                    <span class="result-value">${new Date(data.forecastDate).toLocaleDateString()}</span>
                </div>
                <div class="result-item">
                    <span class="result-label">外温:</span>
                    <span class="result-value">${data.temperature}℃</span>
                </div>
                <div class="success">预测成功完成！</div>
            `;
        }

        function showBatchResult(data) {
            const resultContent = document.getElementById('resultContent');

            let html = '<h4>批量预测结果</h4>';
            data.forEach((item, index) => {
                html += `
                    <div style="border: 1px solid #ddd; margin: 10px 0; padding: 15px; border-radius: 5px;">
                        <h5>预测 ${index + 1}</h5>
                        <div class="result-item">
                            <span class="result-label">日期:</span>
                            <span class="result-value">${new Date(item.forecastDate).toLocaleDateString()}</span>
                        </div>
                        <div class="result-item">
                            <span class="result-label">温度:</span>
                            <span class="result-value">${item.temperature}℃</span>
                        </div>
                        <div class="result-item">
                            <span class="result-label">预测热量:</span>
                            <span class="result-value">${item.forecastHeat ? item.forecastHeat.toFixed(2) : 'N/A'} MW</span>
                        </div>
                        <div class="result-item">
                            <span class="result-label">热指标:</span>
                            <span class="result-value">${item.efficiency ? item.efficiency.toFixed(2) : 'N/A'} W/m²</span>
                        </div>
                    </div>
                `;
            });
            html += '<div class="success">批量预测成功完成！</div>';

            resultContent.innerHTML = html;
        }

        function showError(message) {
            const resultContent = document.getElementById('resultContent');
            resultContent.innerHTML = `<div class="error">错误: ${message}</div>`;

            const resultSection = document.getElementById('resultSection');
            resultSection.classList.add('show');
        }
    </script>
</body>
</html>