package com.thtf.opengis.loadforecast.controller;

import com.thtf.opengis.loadforecast.entity.LoadDay;
import com.thtf.opengis.loadforecast.entity.qo.BatchForecastQO;
import com.thtf.opengis.loadforecast.service.DataGovernanceService;
import com.thtf.opengis.loadforecast.service.IntelligentAssessmentService;
import com.thtf.opengis.loadforecast.service.LoadForecastService;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * 负荷预测控制器
 * 提供负荷预测相关的REST API
 */
@RestController
@RequestMapping("/api/loadforecast")
@CrossOrigin(origins = "*")
public class LoadForecastController {

    @Autowired
    private LoadForecastService loadForecastService;

    @Autowired
    private IntelligentAssessmentService assessmentService;

    @Autowired
    private DataGovernanceService dataGovernanceService;

    /**
     * 执行单次负荷预测
     */
    @PostMapping("/forecast")
    public ResponseEntity<Map<String, Object>> performForecast(@RequestBody ForecastRequest request) {
        try {
            LoadDay result = loadForecastService.performLoadForecast(
                    request.getPeriod(),
                    request.getForecastTarget(),
                    request.getTargetId(),
                    request.getTargetName(),
                    request.getForecastDate(),
                    request.getTemperature(),
                    request.getWeatherCondition()
            );

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", result);
            response.put("message", "预测成功");

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "预测失败: " + e.getMessage());

            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 批量预测
     */
    @PostMapping("/forecast/batch")
    public ResponseEntity<Map<String, Object>> performBatchForecast(@RequestBody BatchForecastQO request) {
        try {
            List<LoadDay> results = loadForecastService.performBatchForecast(request);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", results);
            response.put("count", results.size());
            response.put("message", "批量预测成功");

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "批量预测失败: " + e.getMessage());

            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 更新实际数据
     */
    @PutMapping("/actual/{id}")
    public ResponseEntity<Map<String, Object>> updateActualData(@PathVariable Long id, @RequestBody ActualDataRequest request) {
        try {
            loadForecastService.updateRealData(id, request.getRealHeat(), request.getRealEfficiency());

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "实际数据更新成功");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "更新失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 获取预测历史
     */
    @GetMapping("/history")
    public ResponseEntity<Map<String, Object>> getForecastHistory(
            @RequestParam String period,
            @RequestParam Integer forecastTarget,
            @RequestParam Long targetId,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate) {
        try {
            List<LoadDay> history = loadForecastService.getForecastHistory(period, forecastTarget, targetId, startDate, endDate);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", history);
            response.put("count", history.size());

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "查询失败: " + e.getMessage());

            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 获取预测准确率统计
     */
    @GetMapping("/accuracy")
    public ResponseEntity<Map<String, Object>> getAccuracyStats(
            @RequestParam String period,
            @RequestParam Integer forecastTarget,
            @RequestParam Long targetId) {
        try {
            Map<String, Object> stats = loadForecastService.getForecastAccuracyStats(period, forecastTarget, targetId);
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", stats);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "统计失败: " + e.getMessage());

            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 系统评估
     */
    @PostMapping("/assessment")
    public ResponseEntity<Map<String, Object>> performAssessment(@RequestBody AssessmentRequest request) {
        try {
            LoadDay loadDay = new LoadDay();
            loadDay.setId(request.getLoadDayId());
            loadDay.setPeriod(request.getPeriod());
            loadDay.setForecastTarget(request.getForecastTarget());
            loadDay.setTargetId(request.getTargetId());
            loadDay.setForecastDate(request.getForecastDate());
            loadDay.setTemperature(request.getTemperature());
            loadDay.setEfficiency(request.getEfficiency());
            loadDay.setRealEfficiency(request.getRealEfficiency());
            loadDay.setForecastHeat(request.getForecastHeat());
            loadDay.setRealHeat(request.getRealHeat());

            IntelligentAssessmentService.AssessmentResult result = assessmentService.performSystemAssessment(loadDay);

            List<String> suggestions = assessmentService.generateOptimizationSuggestions(result);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("assessment", result);
            response.put("suggestions", suggestions);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "评估失败: " + e.getMessage());

            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 数据质量评估
     */
    @PostMapping("/dataQuality")
    public ResponseEntity<Map<String, Object>> assessDataQuality(@RequestBody List<LoadDay> data) {
        try {
            Map<String, Object> quality = dataGovernanceService.assessDataQuality(data);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("quality", quality);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "数据质量评估失败: " + e.getMessage());

            return ResponseEntity.badRequest().body(response);
        }
    }

    // 请求对象类
    @Data
    public static class ForecastRequest {
        private String period;
        private Integer forecastTarget;
        private Long targetId;
        private String targetName;
        @DateTimeFormat(pattern = "yyyy-MM-dd")
        private Date forecastDate;
        private Double temperature;
        private String weatherCondition;
    }

    @Data
    public static class ForecastItem {
        @DateTimeFormat(pattern = "yyyy-MM-dd")
        private Date forecastDate;
        private Double temperature;
        private String weatherCondition;
    }

    @Data
    public static class ActualDataRequest {
        private Double realHeat;
        private Double realEfficiency;
    }

    @Data
    public static class AssessmentRequest {
        private Long loadDayId;
        private String period;
        private Integer forecastTarget;
        private Long targetId;
        @DateTimeFormat(pattern = "yyyy-MM-dd")
        private Date forecastDate;
        private Double temperature;
        private Double efficiency;
        private Double realEfficiency;
        private Double forecastHeat;
        private Double realHeat;
    }
}
