package com.thtf.opengis.loadforecast.service;

import com.thtf.opengis.loadforecast.entity.LoadDay;
import com.thtf.opengis.loadforecast.mapper.LoadDayMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 数据治理服务
 * 负责数据清洗、标准化和验证
 */
@Service
public class DataGovernanceService {

    @Autowired
    private LoadDayMapper loadDayMapper;

    // 数据范围常量
    private static final double MIN_TEMPERATURE = -30.0;
    private static final double MAX_TEMPERATURE = 20.0;
    private static final double MIN_EFFICIENCY = 0.0;
    private static final double MAX_EFFICIENCY = 200.0;
    private static final double MIN_HEAT = 0.0;
    private static final double MAX_HEAT = 1000000.0;

    /**
     * 验证输入数据
     */
    public void validateInputData(String period, Integer forecastTarget, Long targetId,
                                Date forecastDate, Double temperature) {
        if (period == null || period.trim().isEmpty()) {
            throw new IllegalArgumentException("采暖期不能为空");
        }

        if (forecastTarget == null || (forecastTarget < 1 || forecastTarget > 3)) {
            throw new IllegalArgumentException("预测对象必须为1(热网)、2(热源)或3(换热站)");
        }

        if (targetId == null || targetId <= 0) {
            throw new IllegalArgumentException("目标ID必须大于0");
        }

        if (forecastDate == null) {
            throw new IllegalArgumentException("预测日期不能为空");
        }

        if (temperature == null) {
            throw new IllegalArgumentException("温度不能为空");
        }

        if (!isValidTemperature(temperature)) {
            throw new IllegalArgumentException(
                String.format("温度值%.2f超出合理范围[%.1f, %.1f]", temperature, MIN_TEMPERATURE, MAX_TEMPERATURE));
        }
    }

    /**
     * 数据清洗 - 去除异常值
     */
    public List<LoadDay> cleanData(List<LoadDay> rawData) {
        return rawData.stream()
                .filter(this::isValidLoadDay)
                .collect(Collectors.toList());
    }

    /**
     * 数据标准化
     */
    public List<LoadDay> standardizeData(List<LoadDay> data) {
        List<LoadDay> standardizedData = new ArrayList<>();

        for (LoadDay loadDay : data) {
            LoadDay standardized = new LoadDay();

            // 复制基本信息
            standardized.setId(loadDay.getId());
            standardized.setPeriod(loadDay.getPeriod());
            standardized.setForecastTarget(loadDay.getForecastTarget());
            standardized.setTargetName(loadDay.getTargetName());
            standardized.setForecastDate(loadDay.getForecastDate());
            standardized.setTargetId(loadDay.getTargetId());

            // 标准化数值数据
            standardized.setTemperature(standardizeTemperature(loadDay.getTemperature()));
            standardized.setArea(standardizeArea(loadDay.getArea()));
            standardized.setForecastHeat(standardizeHeat(loadDay.getForecastHeat()));
            standardized.setRealHeat(standardizeHeat(loadDay.getRealHeat()));
            standardized.setEfficiency(standardizeEfficiency(loadDay.getEfficiency()));
            standardized.setRealEfficiency(standardizeEfficiency(loadDay.getRealEfficiency()));

            standardizedData.add(standardized);
        }

        return standardizedData;
    }

    /**
     * 处理缺失数据
     */
    public List<LoadDay> handleMissingData(List<LoadDay> data) {
        List<LoadDay> processedData = new ArrayList<>();

        for (int i = 0; i < data.size(); i++) {
            LoadDay current = data.get(i);
            LoadDay processed = new LoadDay();

            // 复制所有字段
            copyLoadDay(current, processed);

            // 处理缺失的温度数据
            if (current.getTemperature() == null) {
                processed.setTemperature(interpolateTemperature(data, i));
            }

            // 处理缺失的效率数据
            if (current.getEfficiency() == null) {
                processed.setEfficiency(interpolateEfficiency(data, i));
            }

            // 处理缺失的热量数据
            if (current.getForecastHeat() == null && current.getEfficiency() != null && current.getArea() != null) {
                processed.setForecastHeat(current.getEfficiency() * current.getArea());
            }

            processedData.add(processed);
        }

        return processedData;
    }

    /**
     * 检测异常值
     */
    public List<LoadDay> detectOutliers(List<LoadDay> data) {
        List<LoadDay> outliers = new ArrayList<>();

        // 计算温度的统计信息
        List<Double> temperatures = data.stream()
                .map(LoadDay::getTemperature)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        if (!temperatures.isEmpty()) {
            double[] tempStats = calculateStatistics(temperatures);
            double tempMean = tempStats[0];
            double tempStd = tempStats[1];

            // 使用3σ原则检测温度异常值
            for (LoadDay loadDay : data) {
                if (loadDay.getTemperature() != null) {
                    double zScore = Math.abs(loadDay.getTemperature() - tempMean) / tempStd;
                    if (zScore > 3.0) {
                        outliers.add(loadDay);
                    }
                }
            }
        }

        // 检测效率异常值
        List<Double> efficiencies = data.stream()
                .map(LoadDay::getEfficiency)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        if (!efficiencies.isEmpty()) {
            double[] effStats = calculateStatistics(efficiencies);
            double effMean = effStats[0];
            double effStd = effStats[1];

            for (LoadDay loadDay : data) {
                if (loadDay.getEfficiency() != null) {
                    double zScore = Math.abs(loadDay.getEfficiency() - effMean) / effStd;
                    if (zScore > 3.0 && !outliers.contains(loadDay)) {
                        outliers.add(loadDay);
                    }
                }
            }
        }

        return outliers;
    }

    /**
     * 数据质量评估
     */
    public Map<String, Object> assessDataQuality(List<LoadDay> data) {
        Map<String, Object> quality = new HashMap<>();

        int totalRecords = data.size();
        int validRecords = 0;
        int missingTemperature = 0;
        int missingEfficiency = 0;
        int missingHeat = 0;

        for (LoadDay loadDay : data) {
            if (isValidLoadDay(loadDay)) {
                validRecords++;
            }

            if (loadDay.getTemperature() == null) {
                missingTemperature++;
            }

            if (loadDay.getEfficiency() == null) {
                missingEfficiency++;
            }

            if (loadDay.getForecastHeat() == null) {
                missingHeat++;
            }
        }

        quality.put("totalRecords", totalRecords);
        quality.put("validRecords", validRecords);
        quality.put("validRate", totalRecords > 0 ? (double) validRecords / totalRecords : 0.0);
        quality.put("missingTemperatureRate", totalRecords > 0 ? (double) missingTemperature / totalRecords : 0.0);
        quality.put("missingEfficiencyRate", totalRecords > 0 ? (double) missingEfficiency / totalRecords : 0.0);
        quality.put("missingHeatRate", totalRecords > 0 ? (double) missingHeat / totalRecords : 0.0);

        return quality;
    }

    private boolean isValidLoadDay(LoadDay loadDay) {
        return loadDay != null &&
               isValidTemperature(loadDay.getTemperature()) &&
               isValidEfficiency(loadDay.getEfficiency()) &&
               isValidHeat(loadDay.getForecastHeat()) &&
               isValidArea(loadDay.getArea());
    }

    private boolean isValidTemperature(Double temperature) {
        return temperature != null && temperature >= MIN_TEMPERATURE && temperature <= MAX_TEMPERATURE;
    }

    private boolean isValidEfficiency(Double efficiency) {
        return efficiency == null || (efficiency >= MIN_EFFICIENCY && efficiency <= MAX_EFFICIENCY);
    }

    private boolean isValidHeat(Double heat) {
        return heat == null || (heat >= MIN_HEAT && heat <= MAX_HEAT);
    }

    private boolean isValidArea(Double area) {
        return area == null || area > 0;
    }

    private Double standardizeTemperature(Double temperature) {
        if (temperature == null) return null;
        return Math.round(temperature * 10.0) / 10.0; // 保留1位小数
    }

    private Double standardizeEfficiency(Double efficiency) {
        if (efficiency == null) return null;
        return Math.round(efficiency * 100.0) / 100.0; // 保留2位小数
    }

    private Double standardizeHeat(Double heat) {
        if (heat == null) return null;
        return Math.round(heat * 100.0) / 100.0; // 保留2位小数
    }

    private Double standardizeArea(Double area) {
        if (area == null) return null;
        return Math.round(area * 100.0) / 100.0; // 保留2位小数
    }

    private Double interpolateTemperature(List<LoadDay> data, int index) {
        // 简单线性插值
        Double prevTemp = null;
        Double nextTemp = null;

        // 向前查找
        for (int i = index - 1; i >= 0; i--) {
            if (data.get(i).getTemperature() != null) {
                prevTemp = data.get(i).getTemperature();
                break;
            }
        }

        // 向后查找
        for (int i = index + 1; i < data.size(); i++) {
            if (data.get(i).getTemperature() != null) {
                nextTemp = data.get(i).getTemperature();
                break;
            }
        }

        if (prevTemp != null && nextTemp != null) {
            return (prevTemp + nextTemp) / 2.0;
        } else if (prevTemp != null) {
            return prevTemp;
        } else if (nextTemp != null) {
            return nextTemp;
        } else {
            return 0.0; // 默认值
        }
    }

    private Double interpolateEfficiency(List<LoadDay> data, int index) {
        // 使用相同的插值逻辑
        return interpolateValue(data, index, LoadDay::getEfficiency);
    }

    private Double interpolateValue(List<LoadDay> data, int index, java.util.function.Function<LoadDay, Double> getter) {
        Double prevValue = null;
        Double nextValue = null;

        for (int i = index - 1; i >= 0; i--) {
            Double value = getter.apply(data.get(i));
            if (value != null) {
                prevValue = value;
                break;
            }
        }

        for (int i = index + 1; i < data.size(); i++) {
            Double value = getter.apply(data.get(i));
            if (value != null) {
                nextValue = value;
                break;
            }
        }

        if (prevValue != null && nextValue != null) {
            return (prevValue + nextValue) / 2.0;
        } else if (prevValue != null) {
            return prevValue;
        } else if (nextValue != null) {
            return nextValue;
        } else {
            return null;
        }
    }

    private void copyLoadDay(LoadDay source, LoadDay target) {
        target.setId(source.getId());
        target.setPeriod(source.getPeriod());
        target.setForecastTarget(source.getForecastTarget());
        target.setTargetName(source.getTargetName());
        target.setForecastDate(source.getForecastDate());
        target.setArea(source.getArea());
        target.setTemperature(source.getTemperature());
        target.setForecastHeat(source.getForecastHeat());
        target.setRealHeat(source.getRealHeat());
        target.setEfficiency(source.getEfficiency());
        target.setTargetId(source.getTargetId());
        target.setRealEfficiency(source.getRealEfficiency());
    }

    private double[] calculateStatistics(List<Double> values) {
        double mean = values.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
        double variance = values.stream()
                .mapToDouble(value -> Math.pow(value - mean, 2))
                .average().orElse(0.0);
        double std = Math.sqrt(variance);
        return new double[]{mean, std};
    }
}
