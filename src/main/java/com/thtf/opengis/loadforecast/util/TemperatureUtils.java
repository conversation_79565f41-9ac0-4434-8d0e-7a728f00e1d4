package com.thtf.opengis.loadforecast.util;

import com.thtf.opengis.loadforecast.entity.TemperatureInterval;
import com.thtf.opengis.loadforecast.entity.LoadDay;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 温度区间处理工具类
 */
public class TemperatureUtils {

    /**
     * 创建初始温度区间（每1℃为一个区间）
     */
    public static List<TemperatureInterval> createInitialIntervals(String period, Double minTemp, Double maxTemp) {
        List<TemperatureInterval> intervals = new ArrayList<>();
        int intervalId = 1;
        
        for (double temp = Math.floor(minTemp); temp < Math.ceil(maxTemp); temp += 1.0) {
            TemperatureInterval interval = new TemperatureInterval(
                    intervalId++, temp, temp + 1.0, period);
            intervals.add(interval);
        }
        
        return intervals;
    }

    /**
     * 根据历史数据统计各区间样本数量
     */
    public static List<TemperatureInterval> calculateSampleCounts(List<TemperatureInterval> intervals, 
                                                                 List<LoadDay> historicalData) {
        Map<Integer, Integer> sampleCounts = new HashMap<>();
        
        // 初始化计数
        for (TemperatureInterval interval : intervals) {
            sampleCounts.put(interval.getIntervalId(), 0);
        }
        
        // 统计样本数量
        for (LoadDay data : historicalData) {
            if (data.getTemperature() != null) {
                for (TemperatureInterval interval : intervals) {
                    if (interval.containsTemperature(data.getTemperature())) {
                        sampleCounts.put(interval.getIntervalId(), 
                                sampleCounts.get(interval.getIntervalId()) + 1);
                        break;
                    }
                }
            }
        }
        
        // 更新区间样本数量
        for (TemperatureInterval interval : intervals) {
            interval.setSampleCount(sampleCounts.get(interval.getIntervalId()));
        }
        
        return intervals;
    }

    /**
     * 自适应调整温度区间
     * 当某区间数据量<样本总量5%时，自动合并相邻区间
     */
    public static List<TemperatureInterval> adjustIntervals(List<TemperatureInterval> intervals, 
                                                           int totalSamples, double minSampleRatio) {
        List<TemperatureInterval> adjustedIntervals = new ArrayList<>();
        
        // 按温度排序
        intervals.sort(Comparator.comparing(TemperatureInterval::getMinTemperature));
        
        int i = 0;
        while (i < intervals.size()) {
            TemperatureInterval current = intervals.get(i);
            
            // 检查是否需要合并
            if (current.hasInsufficientSamples(totalSamples) && i < intervals.size() - 1) {
                // 寻找可以合并的相邻区间
                List<TemperatureInterval> toMerge = new ArrayList<>();
                toMerge.add(current);
                
                int j = i + 1;
                int totalMergedSamples = current.getSampleCount();
                
                // 继续合并直到样本数量足够或没有更多区间
                while (j < intervals.size() && 
                       (double) totalMergedSamples / totalSamples < minSampleRatio) {
                    TemperatureInterval next = intervals.get(j);
                    toMerge.add(next);
                    totalMergedSamples += next.getSampleCount();
                    j++;
                }
                
                // 创建合并后的区间
                TemperatureInterval merged = mergeIntervals(toMerge);
                adjustedIntervals.add(merged);
                
                i = j; // 跳过已合并的区间
            } else {
                adjustedIntervals.add(current);
                i++;
            }
        }
        
        return adjustedIntervals;
    }

    /**
     * 合并多个温度区间
     */
    public static TemperatureInterval mergeIntervals(List<TemperatureInterval> intervals) {
        if (intervals.isEmpty()) {
            return null;
        }
        
        if (intervals.size() == 1) {
            return intervals.get(0);
        }
        
        // 找到最小和最大温度
        double minTemp = intervals.stream()
                .mapToDouble(TemperatureInterval::getMinTemperature)
                .min().orElse(0.0);
        double maxTemp = intervals.stream()
                .mapToDouble(TemperatureInterval::getMaxTemperature)
                .max().orElse(0.0);
        
        // 计算总样本数
        int totalSamples = intervals.stream()
                .mapToInt(TemperatureInterval::getSampleCount)
                .sum();
        
        // 创建合并后的区间
        TemperatureInterval merged = new TemperatureInterval(
                intervals.get(0).getIntervalId(), minTemp, maxTemp, intervals.get(0).getPeriod());
        merged.setSampleCount(totalSamples);
        merged.setHeatingStage(intervals.get(0).getHeatingStage());
        
        return merged;
    }

    /**
     * 拆分温度区间（当标准差>0.1时）
     */
    public static List<TemperatureInterval> splitInterval(TemperatureInterval interval) {
        List<TemperatureInterval> splitIntervals = new ArrayList<>();
        
        double minTemp = interval.getMinTemperature();
        double maxTemp = interval.getMaxTemperature();
        double midTemp = (minTemp + maxTemp) / 2.0;
        
        // 创建两个子区间
        TemperatureInterval interval1 = new TemperatureInterval(
                interval.getIntervalId() * 10 + 1, minTemp, midTemp, interval.getPeriod());
        interval1.setHeatingStage(interval.getHeatingStage());
        interval1.setSampleCount(interval.getSampleCount() / 2); // 简化处理
        
        TemperatureInterval interval2 = new TemperatureInterval(
                interval.getIntervalId() * 10 + 2, midTemp, maxTemp, interval.getPeriod());
        interval2.setHeatingStage(interval.getHeatingStage());
        interval2.setSampleCount(interval.getSampleCount() - interval1.getSampleCount());
        
        splitIntervals.add(interval1);
        splitIntervals.add(interval2);
        
        return splitIntervals;
    }

    /**
     * 查找温度所在的区间
     */
    public static TemperatureInterval findIntervalForTemperature(List<TemperatureInterval> intervals, 
                                                               Double temperature) {
        if (temperature == null) {
            return null;
        }
        
        return intervals.stream()
                .filter(interval -> interval.containsTemperature(temperature))
                .findFirst()
                .orElse(null);
    }

    /**
     * 计算区间的修正系数
     */
    public static Double calculateIntervalCoefficient(TemperatureInterval interval, 
                                                     List<LoadDay> intervalData,
                                                     Double designTemperature, 
                                                     Double baseTemperature) {
        if (intervalData.isEmpty()) {
            return 1.0;
        }
        
        List<Double> coefficients = new ArrayList<>();
        
        for (LoadDay data : intervalData) {
            if (data.getRealEfficiency() != null && data.getEfficiency() != null && 
                data.getEfficiency() > 0 && data.getTemperature() != null) {
                
                double ratio = data.getRealEfficiency() / data.getEfficiency();
                double tempAdjustment = (designTemperature - data.getTemperature()) / 
                                      (designTemperature - baseTemperature);
                double coefficient = ratio * tempAdjustment;
                coefficients.add(coefficient);
            }
        }
        
        if (coefficients.isEmpty()) {
            return 1.0;
        }
        
        // 计算平均值
        double mean = coefficients.stream().mapToDouble(Double::doubleValue).average().orElse(1.0);
        
        // 计算标准差
        double variance = coefficients.stream()
                .mapToDouble(coeff -> Math.pow(coeff - mean, 2))
                .average().orElse(0.0);
        double standardDeviation = Math.sqrt(variance);
        
        // 设置区间系数和标准差
        interval.setIntervalCoefficient(mean);
        interval.setStandardDeviation(standardDeviation);
        
        return mean;
    }

    /**
     * 获取区间内的历史数据
     */
    public static List<LoadDay> getIntervalData(TemperatureInterval interval, List<LoadDay> allData) {
        return allData.stream()
                .filter(data -> interval.containsTemperature(data.getTemperature()))
                .collect(Collectors.toList());
    }

    /**
     * 验证区间的有效性
     */
    public static boolean isValidInterval(TemperatureInterval interval) {
        return interval != null &&
               interval.getMinTemperature() != null &&
               interval.getMaxTemperature() != null &&
               interval.getMinTemperature() < interval.getMaxTemperature() &&
               interval.getSampleCount() != null &&
               interval.getSampleCount() >= 0;
    }

    /**
     * 格式化温度区间描述
     */
    public static String formatTemperatureRange(Double minTemp, Double maxTemp) {
        if (minTemp == null || maxTemp == null) {
            return "未知区间";
        }
        return String.format("%.0f~%.0f℃", minTemp, maxTemp);
    }

    /**
     * 计算区间覆盖率
     */
    public static double calculateCoverageRate(List<TemperatureInterval> intervals, 
                                             List<LoadDay> data) {
        if (data.isEmpty()) {
            return 0.0;
        }
        
        long coveredCount = data.stream()
                .filter(loadDay -> loadDay.getTemperature() != null)
                .filter(loadDay -> intervals.stream()
                        .anyMatch(interval -> interval.containsTemperature(loadDay.getTemperature())))
                .count();
        
        return (double) coveredCount / data.size();
    }

    /**
     * 获取区间统计信息
     */
    public static Map<String, Object> getIntervalStatistics(List<TemperatureInterval> intervals) {
        Map<String, Object> stats = new HashMap<>();
        
        int totalIntervals = intervals.size();
        int totalSamples = intervals.stream().mapToInt(TemperatureInterval::getSampleCount).sum();
        
        double minTemp = intervals.stream()
                .mapToDouble(TemperatureInterval::getMinTemperature)
                .min().orElse(0.0);
        double maxTemp = intervals.stream()
                .mapToDouble(TemperatureInterval::getMaxTemperature)
                .max().orElse(0.0);
        
        double avgSamplesPerInterval = totalIntervals > 0 ? (double) totalSamples / totalIntervals : 0.0;
        
        stats.put("totalIntervals", totalIntervals);
        stats.put("totalSamples", totalSamples);
        stats.put("minTemperature", minTemp);
        stats.put("maxTemperature", maxTemp);
        stats.put("temperatureRange", maxTemp - minTemp);
        stats.put("avgSamplesPerInterval", avgSamplesPerInterval);
        
        return stats;
    }
}
