package com.thtf.opengis.loadforecast.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.util.Date;

/**
 * 预测面积实体类
 * 对应表：op_loadarea_t
 */
@Data
@TableName("op_loadarea_t")
public class LoadArea {

    private Long id;

    /**
     * 采暖期
     */
    private String period;

    /**
     * 预测对象(1热网2热源3换热站)
     */
    private Integer forcastTarget;

    /**
     * 对象id
     */
    private Long targetId;

    /**
     * 面积日期
     */
    private Date areaDate;

    /**
     * 建筑性质(1居住2办公3营业)
     */
    private Integer buildingType;

    /**
     * 节能性(1有保温2无保温)
     */
    private Integer insulated;

    /**
     * 正常面积
     */
    private Double areaNormal;

    /**
     * 停暖面积
     */
    private Double areaStop;

    /**
     * 停供面积比例
     */
    private Double stopProportion;

    /**
     * 调节面积
     */
    private Double areaAdjust;

    /**
     * 面积
     */
    private Double area;

    /**
     * 获取预测对象描述
     */
    public String getForecastTargetDesc() {
        switch (forcastTarget) {
            case 1: return "热网";
            case 2: return "热源";
            case 3: return "换热站";
            default: return "未知";
        }
    }

    /**
     * 获取建筑类型描述
     */
    public String getBuildingTypeDesc() {
        switch (buildingType) {
            case 1: return "居住";
            case 2: return "办公";
            case 3: return "营业";
            default: return "未知";
        }
    }

    /**
     * 获取节能性描述
     */
    public String getInsulatedDesc() {
        switch (insulated) {
            case 1: return "有保温";
            case 2: return "无保温";
            default: return "未知";
        }
    }

    /**
     * 计算实际供热面积
     */
    public Double getActualHeatingArea() {
        return areaNormal - areaStop + areaAdjust;
    }
}
