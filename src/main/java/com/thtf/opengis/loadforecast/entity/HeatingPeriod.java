package com.thtf.opengis.loadforecast.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 采暖季阶段实体类
 * 用于采暖季阶段划分和参数配置
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class HeatingPeriod {
    // 采暖季阶段常量
    public static final String STAGE_EARLY_COLD = "初寒期";
    public static final String STAGE_SEVERE_COLD = "严寒期";
    public static final String STAGE_LATE_COLD = "未寒期";

    /**
     * 阶段名称
     */
    private String stageName;

    /**
     * 开始日期
     */
    private Date startDate;

    /**
     * 结束日期
     */
    private Date endDate;

    /**
     * 设计温度
     */
    private Double designTemperature;

    /**
     * 基准温度（平均温度）
     */
    private Double baseTemperature;

    /**
     * 采暖期
     */
    private String period;

    /**
     * 判断指定日期是否在此阶段内
     */
    public boolean containsDate(Date date) {
        if (date == null || startDate == null || endDate == null) {
            return false;
        }
        return !date.before(startDate) && !date.after(endDate);
    }

    /**
     * 获取阶段持续天数
     */
    public long getDurationDays() {
        if (startDate == null || endDate == null) {
            return 0;
        }
        return (endDate.getTime() - startDate.getTime()) / (24 * 60 * 60 * 1000) + 1;
    }

    /**
     * 创建初寒期配置
     */
    public static HeatingPeriod createEarlyColdPeriod(Date startDate, Date endDate, String period) {
        return new HeatingPeriod(STAGE_EARLY_COLD, startDate, endDate, -5.0, -3.0, period);
    }

    /**
     * 创建严寒期配置
     */
    public static HeatingPeriod createSevereColdPeriod(Date startDate, Date endDate, String period) {
        return new HeatingPeriod(STAGE_SEVERE_COLD, startDate, endDate, -8.0, -6.0, period);
    }

    /**
     * 创建未寒期配置
     */
    public static HeatingPeriod createLateColdPeriod(Date startDate, Date endDate, String period) {
        return new HeatingPeriod(STAGE_LATE_COLD, startDate, endDate, 0.0, 2.0, period);
    }
}
