package com.thtf.opengis.loadforecast.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * WeatherDay 实体类，对应数据库表 op_weatherday_t
 *
 * <AUTHOR>
 * @since 2025-05-26
 */
@TableName(value = "op_weatherday_t")
@Data
public class WeatherDay {
    /**
     * *编号
     */
    private Long id;
    /**
     * 机构 ID
     */
    private Long orgId;

    /**
     * 机构名称
     */
    private String orgName;

    /**
     * 日期
     */
    private Date weatherDate;

    /**
     * 日间风向
     */
    private String dayWindDrection;

    /**
     * 日间天气
     */
    private String dayWeather;

    /**
     * 日间风力
     */
    private String dayWindPower;

    /**
     * 日间温度
     */
    private Double dayTemperature;

    /**
     * 夜间天气
     */
    private String nightWeather;

    /**
     * 夜间温度
     */
    private Double nightTemperature;

    /**
     * 夜间风力
     */
    private String nightWindPower;

    /**
     * 夜间风向
     */
    private String nightWindDirection;

    /**
     * 日间实际温度
     */
    private Double dayTemperatureCur;

    /**
     * 夜间实际温度
     */
    private Double nightTemperatureCur;

    /**
     * 最高温度
     */
    private Double tempMax;

    /**
     * 最低温度
     */
    private Double tempMin;

}
