package com.thtf.opengis.loadforecast.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.util.Date;

/**
 * 负荷预测天实体类
 * 对应表：op_loadday_t
 */
@Data
@TableName("op_loadday_t")
public class LoadDay {

    private Long id;

    /**
     * 采暖期
     */
    private String period;

    /**
     * 预测对象(1热网2热源3换热站)
     */
    private Integer forecastTarget;

    /**
     * 对象名称
     */
    private String targetName;

    /**
     * 预测日期
     */
    private Date forecastDate;

    /**
     * 面积
     */
    private Double area;

    /**
     * 外温
     */
    private Double temperature;

    /**
     * 预测热量
     */
    private Double forecastHeat;

    /**
     * 实际热量
     */
    private Double realHeat;

    /**
     * 能耗(w/m)
     */
    private Double efficiency;

    /**
     * 对象id
     */
    private Long targetId;

    /**
     * 实际能耗
     */
    private Double realEfficiency;

    public LoadDay() {}

    // 自定义构造函数
    public LoadDay(String period, Integer forecastTarget, String targetName, Date forecastDate,
                  Double area, Double temperature, Long targetId) {
        this.period = period;
        this.forecastTarget = forecastTarget;
        this.targetName = targetName;
        this.forecastDate = forecastDate;
        this.area = area;
        this.temperature = temperature;
        this.targetId = targetId;
    }

    /**
     * 获取预测对象描述
     */
    public String getForecastTargetDesc() {
        switch (forecastTarget) {
            case 1: return "热网";
            case 2: return "热源";
            case 3: return "换热站";
            default: return "未知";
        }
    }

    /**
     * 计算预测准确率
     */
    public Double getAccuracy() {
        if (realHeat == null || forecastHeat == null || realHeat == 0) {
            return null;
        }
        return 1.0 - Math.abs(forecastHeat - realHeat) / realHeat;
    }

    /**
     * 计算热指标偏差
     */
    public Double getEfficiencyDeviation() {
        if (realEfficiency == null || efficiency == null) {
            return null;
        }
        return realEfficiency - efficiency;
    }
}
