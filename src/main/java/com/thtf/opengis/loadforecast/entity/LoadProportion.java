package com.thtf.opengis.loadforecast.entity;

import lombok.Data;

import java.util.Date;

@Data
public class LoadProportion {

    private Long id;

    private String period;

    private Double temperature;

    private Double proportion;

    private Double efficiency;

    private Long creator;

    private String creatorName;

    private Date createDate;

    private Long updator;

    private String updatorName;

    private Date updateDate;

    private String remark;

    private Boolean valid;
}
