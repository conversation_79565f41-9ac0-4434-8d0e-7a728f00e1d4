package com.thtf.opengis.loadforecast.entity;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * 天气修正系数实体类
 * 用于天气修正系数F2的计算
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class WeatherCorrection {

    /**
     * 天气状况
     */
    private String weatherCondition;

    /**
     * 修正系数
     */
    private Double correctionFactor;

    /**
     * 优先级（用于天气状况选择）
     */
    private Integer priority;

    // 天气状况常量
    public static final String WEATHER_SUNNY = "晴天";
    public static final String WEATHER_CLOUDY = "阴天";
    public static final String WEATHER_LIGHT_SNOW = "小雪";
    public static final String WEATHER_HEAVY_SNOW = "大雪";

    /**
     * 获取默认天气修正系数配置
     */
    public static WeatherCorrection[] getDefaultWeatherCorrections() {
        return new WeatherCorrection[] {
            new WeatherCorrection(WEATHER_HEAVY_SNOW, 1.08, 1),  // 最高优先级
            new WeatherCorrection(WEATHER_LIGHT_SNOW, 1.05, 2),
            new WeatherCorrection(WEATHER_CLOUDY, 1.05, 3),
            new WeatherCorrection(WEATHER_SUNNY, 0.98, 4)        // 最低优先级
        };
    }

    /**
     * 根据天气状况获取修正系数
     * 取值规则：有雪取雪，无雪取阴，无阴取晴
     */
    public static Double getCorrectionFactorByWeather(String[] weatherConditions) {
        if (weatherConditions == null || weatherConditions.length == 0) {
            return 0.98; // 默认晴天系数
        }

        // 按优先级检查天气状况
        for (String weather : weatherConditions) {
            if (weather != null) {
                if (weather.contains("大雪")) {
                    return 1.08;
                } else if (weather.contains("小雪") || weather.contains("雪")) {
                    return 1.05;
                } else if (weather.contains("阴")) {
                    return 1.05;
                } else if (weather.contains("晴")) {
                    return 0.98;
                }
            }
        }

        return 0.98; // 默认晴天系数
    }

    /**
     * 根据单个天气状况获取修正系数
     */
    public static Double getCorrectionFactorByWeather(String weatherCondition) {
        return getCorrectionFactorByWeather(new String[]{weatherCondition});
    }
}
