package com.thtf.opengis.loadforecast.entity;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * 温度区间实体类
 * 用于历史数据映射和修正系数计算
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class TemperatureInterval {

    /**
     * 区间号
     */
    private Integer intervalId;

    /**
     * 温度区间描述，如"-8~-5℃"
     */
    private String temperatureRange;

    /**
     * 最低温度
     */
    private Double minTemperature;

    /**
     * 最高温度
     */
    private Double maxTemperature;

    /**
     * 样本数量
     */
    private Integer sampleCount;

    /**
     * 区间系数K1
     */
    private Double intervalCoefficient;

    /**
     * 标准差
     */
    private Double standardDeviation;

    /**
     * 采暖期
     */
    private String period;

    /**
     * 采暖季阶段（初寒期、严寒期、未寒期）
     */
    private String heatingStage;

    // 自定义构造函数
    public TemperatureInterval(Integer intervalId, Double minTemperature, Double maxTemperature, String period) {
        this.intervalId = intervalId;
        this.minTemperature = minTemperature;
        this.maxTemperature = maxTemperature;
        this.period = period;
        this.temperatureRange = String.format("%.0f~%.0f℃", minTemperature, maxTemperature);
        this.sampleCount = 0;
    }

    /**
     * 更新温度区间描述
     */
    private void updateTemperatureRange() {
        if (minTemperature != null && maxTemperature != null) {
            this.temperatureRange = String.format("%.0f~%.0f℃", minTemperature, maxTemperature);
        }
    }

    /**
     * 判断温度是否在此区间内
     */
    public boolean containsTemperature(Double temperature) {
        if (temperature == null || minTemperature == null || maxTemperature == null) {
            return false;
        }
        return temperature >= minTemperature && temperature <= maxTemperature;
    }

    /**
     * 获取区间中心温度
     */
    public Double getCenterTemperature() {
        if (minTemperature == null || maxTemperature == null) {
            return null;
        }
        return (minTemperature + maxTemperature) / 2.0;
    }

    /**
     * 获取区间宽度
     */
    public Double getIntervalWidth() {
        if (minTemperature == null || maxTemperature == null) {
            return null;
        }
        return maxTemperature - minTemperature;
    }

    /**
     * 判断是否需要拆分区间（标准差>0.1）
     */
    public boolean needsSplit() {
        return standardDeviation != null && standardDeviation > 0.1;
    }

    /**
     * 判断样本数量是否过少（<总样本5%）
     */
    public boolean hasInsufficientSamples(Integer totalSamples) {
        if (sampleCount == null || totalSamples == null || totalSamples == 0) {
            return true;
        }
        return (double) sampleCount / totalSamples < 0.05;
    }
}
