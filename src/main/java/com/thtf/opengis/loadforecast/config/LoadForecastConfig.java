package com.thtf.opengis.loadforecast.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 负荷预测配置类
 */
@Configuration
@ConfigurationProperties(prefix = "loadforecast")
public class LoadForecastConfig {

    /**
     * 算法参数配置
     */
    private Algorithm algorithm = new Algorithm();

    /**
     * 数据治理配置
     */
    private DataGovernance dataGovernance = new DataGovernance();

    /**
     * 评估配置
     */
    private Assessment assessment = new Assessment();

    public Algorithm getAlgorithm() {
        return algorithm;
    }

    public void setAlgorithm(Algorithm algorithm) {
        this.algorithm = algorithm;
    }

    public DataGovernance getDataGovernance() {
        return dataGovernance;
    }

    public void setDataGovernance(DataGovernance dataGovernance) {
        this.dataGovernance = dataGovernance;
    }

    public Assessment getAssessment() {
        return assessment;
    }

    public void setAssessment(Assessment assessment) {
        this.assessment = assessment;
    }

    /**
     * 算法参数配置
     */
    public static class Algorithm {
        /**
         * 默认长输管网热损修正系数F1
         */
        private Double defaultF1 = 1.0;

        /**
         * 温度区间最小样本比例阈值
         */
        private Double minSampleRatio = 0.05;

        /**
         * 区间拆分标准差阈值
         */
        private Double splitStdThreshold = 0.1;

        /**
         * 默认K1系数
         */
        private Double defaultK1 = 1.0;

        /**
         * 默认K2系数
         */
        private Double defaultK2 = 1.0;

        /**
         * 采暖季阶段配置
         */
        private HeatingStages heatingStages = new HeatingStages();

        public Double getDefaultF1() {
            return defaultF1;
        }

        public void setDefaultF1(Double defaultF1) {
            this.defaultF1 = defaultF1;
        }

        public Double getMinSampleRatio() {
            return minSampleRatio;
        }

        public void setMinSampleRatio(Double minSampleRatio) {
            this.minSampleRatio = minSampleRatio;
        }

        public Double getSplitStdThreshold() {
            return splitStdThreshold;
        }

        public void setSplitStdThreshold(Double splitStdThreshold) {
            this.splitStdThreshold = splitStdThreshold;
        }

        public Double getDefaultK1() {
            return defaultK1;
        }

        public void setDefaultK1(Double defaultK1) {
            this.defaultK1 = defaultK1;
        }

        public Double getDefaultK2() {
            return defaultK2;
        }

        public void setDefaultK2(Double defaultK2) {
            this.defaultK2 = defaultK2;
        }

        public HeatingStages getHeatingStages() {
            return heatingStages;
        }

        public void setHeatingStages(HeatingStages heatingStages) {
            this.heatingStages = heatingStages;
        }
    }

    /**
     * 采暖季阶段配置
     */
    public static class HeatingStages {
        private StageConfig earlyCold = new StageConfig(-5.0, -3.0);
        private StageConfig severeCold = new StageConfig(-8.0, -6.0);
        private StageConfig lateCold = new StageConfig(0.0, 2.0);

        public StageConfig getEarlyCold() {
            return earlyCold;
        }

        public void setEarlyCold(StageConfig earlyCold) {
            this.earlyCold = earlyCold;
        }

        public StageConfig getSevereCold() {
            return severeCold;
        }

        public void setSevereCold(StageConfig severeCold) {
            this.severeCold = severeCold;
        }

        public StageConfig getLateCold() {
            return lateCold;
        }

        public void setLateCold(StageConfig lateCold) {
            this.lateCold = lateCold;
        }
    }

    /**
     * 阶段配置
     */
    public static class StageConfig {
        private Double designTemperature;
        private Double baseTemperature;

        public StageConfig() {}

        public StageConfig(Double designTemperature, Double baseTemperature) {
            this.designTemperature = designTemperature;
            this.baseTemperature = baseTemperature;
        }

        public Double getDesignTemperature() {
            return designTemperature;
        }

        public void setDesignTemperature(Double designTemperature) {
            this.designTemperature = designTemperature;
        }

        public Double getBaseTemperature() {
            return baseTemperature;
        }

        public void setBaseTemperature(Double baseTemperature) {
            this.baseTemperature = baseTemperature;
        }
    }

    /**
     * 数据治理配置
     */
    public static class DataGovernance {
        /**
         * 温度范围
         */
        private Double minTemperature = -30.0;
        private Double maxTemperature = 20.0;

        /**
         * 效率范围
         */
        private Double minEfficiency = 0.0;
        private Double maxEfficiency = 200.0;

        /**
         * 热量范围
         */
        private Double minHeat = 0.0;
        private Double maxHeat = 1000000.0;

        /**
         * 异常值检测阈值（3σ原则）
         */
        private Double outlierThreshold = 3.0;

        public Double getMinTemperature() {
            return minTemperature;
        }

        public void setMinTemperature(Double minTemperature) {
            this.minTemperature = minTemperature;
        }

        public Double getMaxTemperature() {
            return maxTemperature;
        }

        public void setMaxTemperature(Double maxTemperature) {
            this.maxTemperature = maxTemperature;
        }

        public Double getMinEfficiency() {
            return minEfficiency;
        }

        public void setMinEfficiency(Double minEfficiency) {
            this.minEfficiency = minEfficiency;
        }

        public Double getMaxEfficiency() {
            return maxEfficiency;
        }

        public void setMaxEfficiency(Double maxEfficiency) {
            this.maxEfficiency = maxEfficiency;
        }

        public Double getMinHeat() {
            return minHeat;
        }

        public void setMinHeat(Double minHeat) {
            this.minHeat = minHeat;
        }

        public Double getMaxHeat() {
            return maxHeat;
        }

        public void setMaxHeat(Double maxHeat) {
            this.maxHeat = maxHeat;
        }

        public Double getOutlierThreshold() {
            return outlierThreshold;
        }

        public void setOutlierThreshold(Double outlierThreshold) {
            this.outlierThreshold = outlierThreshold;
        }
    }

    /**
     * 评估配置
     */
    public static class Assessment {
        /**
         * 相似度阈值（5%）
         */
        private Double similarityThreshold = 0.05;

        /**
         * 准确率阈值（90%）
         */
        private Double accuracyThreshold = 0.9;

        /**
         * 差异警报阈值
         */
        private Double differenceAlertThreshold = 0.1;

        /**
         * 能耗超标阈值
         */
        private Double energyExceededThreshold = 0.2;

        public Double getSimilarityThreshold() {
            return similarityThreshold;
        }

        public void setSimilarityThreshold(Double similarityThreshold) {
            this.similarityThreshold = similarityThreshold;
        }

        public Double getAccuracyThreshold() {
            return accuracyThreshold;
        }

        public void setAccuracyThreshold(Double accuracyThreshold) {
            this.accuracyThreshold = accuracyThreshold;
        }

        public Double getDifferenceAlertThreshold() {
            return differenceAlertThreshold;
        }

        public void setDifferenceAlertThreshold(Double differenceAlertThreshold) {
            this.differenceAlertThreshold = differenceAlertThreshold;
        }

        public Double getEnergyExceededThreshold() {
            return energyExceededThreshold;
        }

        public void setEnergyExceededThreshold(Double energyExceededThreshold) {
            this.energyExceededThreshold = energyExceededThreshold;
        }
    }
}
