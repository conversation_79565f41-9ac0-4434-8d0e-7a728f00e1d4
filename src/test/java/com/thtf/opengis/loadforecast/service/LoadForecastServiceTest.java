package com.thtf.opengis.loadforecast.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import com.thtf.opengis.loadforecast.entity.LoadDay;
import com.thtf.opengis.loadforecast.entity.qo.BatchForecastQO;
import com.thtf.opengis.loadforecast.mapper.LoadAreaMapper;
import com.thtf.opengis.loadforecast.mapper.LoadParamMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 负荷预测服务测试类
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class LoadForecastServiceTest {

    @Autowired
    private LoadForecastService loadForecastService;

    @Autowired
    private AlgorithmCalculationService algorithmService;

    @Autowired
    private DataGovernanceService dataGovernanceService;

    @Autowired
    private IntelligentAssessmentService assessmentService;

    @Autowired
    private LoadParamMapper loadParamMapper;

    @Autowired
    private LoadAreaMapper loadAreaMapper;

    @Test
    public void testLoadForecast() {
        try {

            // 执行负荷预测
            Date forecastDate = new Date();
            LoadDay result = loadForecastService.performLoadForecast(
                    "2024-2025", 1, 1L, "测试热网",
                    forecastDate, -5.0, "晴天"
            );

            System.out.println("预测结果: " + result);

            // 验证结果
            assert result != null;
            assert result.getForecastHeat() != null;
            assert result.getEfficiency() != null;

            System.out.println("负荷预测测试通过");

        } catch (Exception e) {
            System.err.println("负荷预测测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Test
    public void testAlgorithmCalculation() {
        try {
            // 测试标杆热指标计

            Double benchmarkIndex = algorithmService.calculateBenchmarkHeatIndex("2024-2025", -5.0, -5.0, 1, 1L);

            System.out.println("标杆热指标: " + benchmarkIndex);
            assert benchmarkIndex != null;
            assert benchmarkIndex > 0;

            // 测试修正系数计算
            Double k2 = algorithmService.calculateRealtimeCorrectionK2("2024-2025", 2, 1L, new Date());

            System.out.println("实时修正系数K2: " + k2);
            assert k2 != null;

            System.out.println("算法计算测试通过");

        } catch (Exception e) {
            System.err.println("算法计算测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Test
    public void testDataGovernance() {
        try {
            // 创建测试数据
            List<LoadDay> testData = createTestLoadDayData();

            // 测试数据清洗
            List<LoadDay> cleanedData = dataGovernanceService.cleanData(testData);
            System.out.println("清洗前数据量: " + testData.size() + ", 清洗后数据量: " + cleanedData.size());

            // 测试数据标准化
            List<LoadDay> standardizedData = dataGovernanceService.standardizeData(cleanedData);
            System.out.println("标准化数据量: " + standardizedData.size());

            // 测试数据质量评估
            Map<String, Object> quality = dataGovernanceService.assessDataQuality(testData);
            System.out.println("数据质量评估: " + quality);

            assert quality.containsKey("totalRecords");
            assert quality.containsKey("validRecords");
            assert quality.containsKey("validRate");

            System.out.println("数据治理测试通过");

        } catch (Exception e) {
            System.err.println("数据治理测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Test
    public void testIntelligentAssessment() {
        try {
            // 创建测试LoadDay
            LoadDay testLoadDay = createTestLoadDay();

            // 执行系统评估
            IntelligentAssessmentService.AssessmentResult result = assessmentService.performSystemAssessment(testLoadDay);

            System.out.println("评估结果: " + result.getStatus());
            System.out.println("评估描述: " + result.getDescription());
            System.out.println("建议操作: " + result.getRecommendation());
            System.out.println("指标: " + result.getIndicators());
            System.out.println("警报: " + result.getAlerts());

            // 生成优化建议
            List<String> suggestions = assessmentService.generateOptimizationSuggestions(result);
            System.out.println("优化建议: " + suggestions);

            assert result != null;
            assert result.getStatus() != null;
            assert result.getIndicators() != null;

            System.out.println("智能评估测试通过");

        } catch (Exception e) {
            System.err.println("智能评估测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Test
    public void testBatchForecast() {
        try {

            // 创建批量预测请求
            BatchForecastQO request = new BatchForecastQO();
            request.setPeriod("2024-2025");
            request.setForecastTarget(2);
            request.setTargetId(1L);
            request.setTargetName("测试热网");
            request.setStartDate(DateUtil.parseDate("2025-02-01"));
            request.setEndDate(DateUtil.parseDate("2025-02-28"));
            // 执行批量预测
            List<LoadDay> results = loadForecastService.performBatchForecast(request);

            System.out.println("批量预测结果数量: " + results.size());

            for (LoadDay result : results) {
                System.out.println("预测日期: " + result.getForecastDate() +
                                   ", 温度: " + result.getTemperature() +
                                   ", 预测热量: " + NumberUtil.round(result.getForecastHeat() / 10000, 2).toString());
            }

            System.out.println("批量预测测试通过");

        } catch (Exception e) {
            System.err.println("批量预测测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 创建测试LoadDay数据
     */
    private List<LoadDay> createTestLoadDayData() {
        List<LoadDay> data = new ArrayList<>();
        Calendar cal = Calendar.getInstance();

        for (int i = 0; i < 10; i++) {
            LoadDay loadDay = new LoadDay();
            loadDay.setId((long) (i + 1));
            loadDay.setPeriod("2023-2024");
            loadDay.setForecastTarget(1);
            loadDay.setTargetId(1L);
            loadDay.setTargetName("测试热网");
            loadDay.setForecastDate(cal.getTime());
            loadDay.setTemperature(-5.0 + i);
            loadDay.setArea(15000.0);
            loadDay.setForecastHeat(1000.0 + i * 100);
            loadDay.setRealHeat(950.0 + i * 95);
            loadDay.setEfficiency(50.0 + i);
            loadDay.setRealEfficiency(48.0 + i);

            data.add(loadDay);
            cal.add(Calendar.DAY_OF_MONTH, 1);
        }

        // 添加一些异常数据
        LoadDay outlier = new LoadDay();
        outlier.setId(11L);
        outlier.setPeriod("2023-2024");
        outlier.setForecastTarget(1);
        outlier.setTargetId(1L);
        outlier.setTemperature(100.0); // 异常温度
        outlier.setEfficiency(500.0); // 异常效率
        data.add(outlier);

        return data;
    }

    /**
     * 创建测试LoadDay
     */
    private LoadDay createTestLoadDay() {
        LoadDay loadDay = new LoadDay();
        loadDay.setId(1L);
        loadDay.setPeriod("2023-2024");
        loadDay.setForecastTarget(1);
        loadDay.setTargetId(1L);
        loadDay.setTargetName("测试热网");
        loadDay.setForecastDate(new Date());
        loadDay.setTemperature(-5.0);
        loadDay.setArea(15000.0);
        loadDay.setForecastHeat(1000.0);
        loadDay.setRealHeat(950.0);
        loadDay.setEfficiency(50.0);
        loadDay.setRealEfficiency(48.0);

        return loadDay;
    }
}
