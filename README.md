# 供热负荷预测系统

基于历史数据与动态修正的智能供热负荷预测系统，采用Java Spring Boot框架开发，实现了三维评价体系（预测指标、标杆指标、实际指标）和智能评估功能。

## 🎯 系统特性

### 核心功能
- **三维评价体系**：预测指标、标杆指标、实际指标的全面评估
- **动态修正算法**：历史修正系数K1和实时修正系数K2的双重修正
- **智能评估系统**：自动评估系统运行状态，提供优化建议
- **数据治理模块**：数据清洗、标准化和质量评估

### 技术特点
- **采暖季阶段划分**：自动划分初寒期、严寒期、未寒期
- **温度区间自适应调整**：动态合并和拆分温度区间
- **天气修正系数**：根据天气状况自动调整预测结果
- **批量预测支持**：支持单次和批量负荷预测

## 🏗️ 系统架构

```
├── entity/              # 实体类
│   ├── LoadDay.java            # 负荷预测天实体
│   ├── LoadArea.java           # 预测面积实体
│   ├── LoadParam.java          # 预测参数实体
│   ├── LoadProportion.java     # 负荷百分比实体
│   ├── TemperatureInterval.java # 温度区间实体
│   ├── HeatingPeriod.java      # 采暖季阶段实体
│   └── WeatherCorrection.java  # 天气修正实体
├── mapper/              # 数据访问层
│   ├── LoadDayMapper.java      # 负荷预测天数据访问
│   ├── LoadAreaMapper.java     # 预测面积数据访问
│   ├── LoadParamMapper.java    # 预测参数数据访问
│   └── LoadProportionMapper.java # 负荷百分比数据访问
├── service/             # 服务层
│   ├── LoadForecastService.java      # 负荷预测主服务
│   ├── AlgorithmCalculationService.java # 算法计算服务
│   ├── DataGovernanceService.java    # 数据治理服务
│   └── IntelligentAssessmentService.java # 智能评估服务
├── controller/          # 控制器层
│   └── LoadForecastController.java   # 负荷预测API控制器
├── config/              # 配置类
│   └── LoadForecastConfig.java      # 系统配置
└── util/                # 工具类
    └── TemperatureUtils.java        # 温度区间处理工具
```

## 📊 数据库表结构

系统使用MySQL数据库，包含以下核心表：

- **op_loadday_t**: 负荷预测天数据表
- **op_loadarea_t**: 预测面积数据表  
- **op_loadparam_t**: 预测参数数据表
- **op_loadproportion_t**: 负荷百分比数据表

详细表结构请参考 `表结构.md` 文件。

## 🚀 快速开始

### 环境要求
- Java 8+
- MySQL 5.7+
- Maven 3.6+

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd loadforecast
```

2. **配置数据库**
```sql
-- 创建数据库
CREATE DATABASE loadforecast CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 执行表结构.md中的SQL脚本创建表
```

3. **修改配置文件**
编辑 `src/main/resources/application.yml`，修改数据库连接信息：
```yaml
spring:
  datasource:
    url: *******************************************************************************************************************
    username: your_username
    password: your_password
```

4. **编译运行**
```bash
mvn clean compile
mvn spring-boot:run
```

5. **访问系统**
打开浏览器访问：http://localhost:8080

## 📡 API 接口

### 负荷预测接口

#### 单次预测
```http
POST /api/loadforecast/forecast
Content-Type: application/json

{
  "period": "2023-2024",
  "forecastTarget": 1,
  "targetId": 1,
  "targetName": "测试热网",
  "forecastDate": "2024-01-01",
  "temperature": -5.0,
  "weatherCondition": "晴天"
}
```

#### 批量预测
```http
POST /api/loadforecast/forecast/batch
Content-Type: application/json

{
  "period": "2023-2024",
  "forecastTarget": 1,
  "targetId": 1,
  "targetName": "测试热网",
  "items": [
    {
      "forecastDate": "2024-01-01",
      "temperature": -5.0,
      "weatherCondition": "晴天"
    }
  ]
}
```

#### 更新实际数据
```http
PUT /api/loadforecast/actual/{id}
Content-Type: application/json

{
  "realHeat": 1000.0,
  "realEfficiency": 50.0
}
```

#### 获取预测历史
```http
GET /api/loadforecast/history?period=2023-2024&forecastTarget=1&targetId=1&startDate=2024-01-01&endDate=2024-01-31
```

#### 获取预测准确率统计
```http
GET /api/loadforecast/accuracy?period=2023-2024&forecastTarget=1&targetId=1
```

#### 系统评估
```http
POST /api/loadforecast/assessment
Content-Type: application/json

{
  "loadDayId": 1,
  "period": "2023-2024",
  "forecastTarget": 1,
  "targetId": 1,
  "forecastDate": "2024-01-01",
  "temperature": -5.0,
  "efficiency": 50.0,
  "realEfficiency": 48.0,
  "forecastHeat": 1000.0,
  "realHeat": 950.0
}
```

## 🧮 算法说明

### 标杆热指标计算
```
标杆热指标 = (W1 × M1 + W2 × M2) × (18 - T实际) / (18 - T设计)
```
- W1：节能建筑采暖热指标(W/m²)
- W2：非节能建筑采暖热指标(W/m²)  
- M1：节能建筑占总面积比例
- M2：非节能建筑占总面积比例

### 修正系数计算

**历史修正系数K1：**
```
K1 = (∑W实际 / ∑W预测) × (T设计 - T实际) / (T设计 - T基准)
```

**实时修正系数K2：**
```
K2 = (1/n) × ∑(W实际(i) / W预测(i))
```

### 预测热指标计算
```
预测热指标 = 标杆热指标 × K1 × K2
```

### 热负荷计算
```
热负荷 = 对应热指标 × 实际供热面积 × F1 × F2
```
- F1：长输管网热损修正系数（默认1.0）
- F2：天气修正系数

## 🔧 配置说明

系统支持通过 `application.yml` 进行详细配置：

```yaml
loadforecast:
  algorithm:
    default-f1: 1.0                    # 默认F1系数
    min-sample-ratio: 0.05             # 最小样本比例
    split-std-threshold: 0.1           # 区间拆分标准差阈值
    heating-stages:                    # 采暖季阶段配置
      early-cold:
        design-temperature: -5.0
        base-temperature: -3.0
  data-governance:
    min-temperature: -30.0             # 最小温度范围
    max-temperature: 20.0              # 最大温度范围
    outlier-threshold: 3.0             # 异常值检测阈值
  assessment:
    similarity-threshold: 0.05         # 相似度阈值
    accuracy-threshold: 0.9            # 准确率阈值
```

## 🧪 测试

运行单元测试：
```bash
mvn test
```

测试类包含了各个模块的功能测试：
- 负荷预测测试
- 算法计算测试  
- 数据治理测试
- 智能评估测试
- 批量预测测试

## 📈 系统评估

系统提供四种运行状态评估：

1. **系统正常**：预测指标≈标杆指标≈实际指标
2. **能耗超标**：标杆指标<预测指标≈实际指标  
3. **系统最优**：标杆指标>预测指标≈实际指标
4. **模型修正**：预测指标≠实际指标

每种状态都会提供相应的优化建议和操作指导。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进系统功能。

## 📄 许可证

本项目采用MIT许可证，详情请参考LICENSE文件。
